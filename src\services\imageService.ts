/**
 * 图片服务
 * 基于后端API文档的图片接口实现
 */

import { request } from '@/lib/http';
import type {
  ImageUploadRequest,
  ImageUploadResponse,
  ImageListParams,
  ImageListResponse,
  ImageDeleteRequest,
  ImageDeleteResponse,
  ImageInfo,
  ImageCategory,
} from '@/types/image';

/**
 * 图片上传服务
 * POST /admin/images/upload
 */
export async function uploadImage(uploadData: ImageUploadRequest): Promise<ImageInfo> {
  const formData = new FormData();

  // 添加文件
  formData.append('file', uploadData.file);

  // 添加元数据
  formData.append('category', uploadData.category);
  formData.append('resourceId', uploadData.resourceId);
  formData.append('imageType', uploadData.imageType);
  formData.append('variant', uploadData.variant);

  if (uploadData.groupId) {
    formData.append('groupId', uploadData.groupId);
  }

  const response = await request<any>('/admin/images/upload', {
    method: 'POST',
    body: formData,
    headers: {
      // 不设置Content-Type，让浏览器自动设置multipart/form-data边界
    },
  });

  // 处理后端响应格式，转换为前端期望的格式
  const item = response.data || response;
  return {
    id: item.id,
    groupId: item.group_id,
    relativePath: `/images/${item.id}/file`, // 使用正确的图片访问格式
    variant: item.variant || 'original',
    metadata: {
      size: item.file_size || 0,
      dimensions: {
        width: item.width || 0,
        height: item.height || 0,
      },
      format: item.format || 'jpeg',
    },
    createdAt: item.created_at,
    updatedAt: item.updated_at,
  };
}

/**
 * 批量上传图片
 */
export async function uploadImages(
  files: File[],
  metadata: Omit<ImageUploadRequest, 'file'>
): Promise<ImageInfo[]> {
  const uploadPromises = files.map(file => 
    uploadImage({ ...metadata, file })
  );
  
  return Promise.all(uploadPromises);
}

/**
 * 获取图片列表
 * GET /images/{category}/{resourceId}
 */
export async function getImages(params: ImageListParams): Promise<ImageListResponse['data']> {
  const { category, resourceId, ...queryParams } = params;

  // 构建查询字符串
  const searchParams = new URLSearchParams();
  Object.entries(queryParams).forEach(([key, value]) => {
    if (value !== undefined) {
      searchParams.append(key, String(value));
    }
  });

  const queryString = searchParams.toString();
  const url = `/images/${category}/${resourceId}${queryString ? `?${queryString}` : ''}`;

  console.log('Fetching images from:', url, 'with params:', params);

  try {
    const response = await request<any>(url, {
      method: 'GET',
    });

    console.log('Images API response:', response);

    // 处理后端响应格式，转换为前端期望的格式
    const backendData = response.data || response;
    const images = (backendData.images || []).map((item: any) => ({
      id: item.id,
      groupId: item.group_id,
      relativePath: `/images/${item.id}/file`, // 使用正确的图片访问格式
      variant: item.variant || 'original',
      metadata: {
        size: item.file_size || 0,
        dimensions: {
          width: item.width || 0,
          height: item.height || 0,
        },
        format: item.format || 'jpeg',
      },
      createdAt: item.created_at,
      updatedAt: item.updated_at,
    }));

    return {
      images,
      pagination: backendData.pagination || {
        page: 1,
        pageSize: 20,
        total: images.length,
        totalPages: 1,
      },
    };
  } catch (error) {
    console.error('Failed to fetch images:', error);
    // 返回空数据而不是抛出错误，避免页面崩溃
    return {
      images: [],
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0,
        totalPages: 0,
      },
    };
  }
}

/**
 * 获取单个图片详情
 * GET /images/{id}
 */
export async function getImageById(id: string): Promise<ImageInfo> {
  const response = await request<any>(`/images/${id}`, {
    method: 'GET',
  });

  // 处理后端响应格式，转换为前端期望的格式
  const item = response.data || response;
  return {
    id: item.id,
    groupId: item.group_id,
    relativePath: `/images/${item.id}/file`, // 使用正确的图片访问格式
    variant: item.variant || 'original',
    metadata: {
      size: item.file_size || 0,
      dimensions: {
        width: item.width || 0,
        height: item.height || 0,
      },
      format: item.format || 'jpeg',
    },
    createdAt: item.created_at,
    updatedAt: item.updated_at,
  };
}

/**
 * 删除图片
 * DELETE /admin/images
 */
export async function deleteImages(relativePaths: string[]): Promise<ImageDeleteResponse['data']> {
  const response = await request<ImageDeleteResponse>('/admin/images', {
    method: 'DELETE',
    body: JSON.stringify({ relativePaths }),
  });

  return response.data;
}

/**
 * 删除单个图片
 */
export async function deleteImage(relativePath: string): Promise<void> {
  await deleteImages([relativePath]);
}

/**
 * 生成图片URL
 * 根据相对路径生成完整的图片访问URL
 */
export function getImageUrl(relativePath: string, baseUrl?: string): string {
  if (!relativePath || typeof relativePath !== 'string') {
    console.warn('getImageUrl: relativePath is invalid', relativePath);
    return '';
  }

  const base = baseUrl || process.env.NEXT_PUBLIC_CDN_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787';

  // 确保base URL不以/结尾，relativePath以/开头
  const cleanBase = base.replace(/\/$/, '');
  const cleanPath = relativePath.startsWith('/') ? relativePath : `/${relativePath}`;

  return `${cleanBase}${cleanPath}`;
}

/**
 * 生成缩略图URL
 * 对于新的API格式 /images/{imageId}/file，缩略图和原图使用相同的URL
 */
export function getThumbnailUrl(relativePath: string): string {
  // 检查输入参数
  if (!relativePath || typeof relativePath !== 'string') {
    console.warn('getThumbnailUrl: relativePath is invalid', relativePath);
    return '';
  }

  // 对于新的 /images/{imageId}/file 格式，直接返回相同的URL
  // 后端会根据请求参数或者默认返回合适的尺寸
  return getImageUrl(relativePath);
}

/**
 * 验证图片文件
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Unsupported file type. Please upload JPEG, PNG, WebP, or GIF images.',
    };
  }

  // 检查文件大小 (100字节 - 10MB)
  const minSize = 100;
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (file.size < minSize) {
    return {
      valid: false,
      error: 'File is too small. Minimum size is 100 bytes.',
    };
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File is too large. Maximum size is 10MB.',
    };
  }

  return { valid: true };
}

/**
 * 批量验证图片文件
 */
export function validateImageFiles(files: File[]): {
  valid: File[];
  invalid: Array<{ file: File; error: string }>;
} {
  const valid: File[] = [];
  const invalid: Array<{ file: File; error: string }> = [];

  files.forEach(file => {
    const validation = validateImageFile(file);
    if (validation.valid) {
      valid.push(file);
    } else {
      invalid.push({ file, error: validation.error! });
    }
  });

  return { valid, invalid };
}

/**
 * 获取文件预览URL
 */
export function getFilePreviewUrl(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

/**
 * 压缩图片文件
 */
export function compressImage(
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
  } = {}
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      const { maxWidth = 1920, maxHeight = 1080, quality = 0.8 } = options;
      
      // 计算新尺寸
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
}

/**
 * 图片服务类
 * 提供统一的图片操作接口
 */
export class ImageService {
  /**
   * 上传图片
   */
  static async upload(uploadData: ImageUploadRequest): Promise<ImageInfo> {
    return uploadImage(uploadData);
  }

  /**
   * 批量上传
   */
  static async uploadBatch(
    files: File[],
    metadata: Omit<ImageUploadRequest, 'file'>
  ): Promise<ImageInfo[]> {
    return uploadImages(files, metadata);
  }

  /**
   * 获取图片列表
   */
  static async list(params: ImageListParams): Promise<ImageListResponse['data']> {
    return getImages(params);
  }

  /**
   * 获取图片详情
   */
  static async getById(id: string): Promise<ImageInfo> {
    return getImageById(id);
  }

  /**
   * 删除图片
   */
  static async delete(relativePaths: string[]): Promise<ImageDeleteResponse['data']> {
    return deleteImages(relativePaths);
  }

  /**
   * 验证文件
   */
  static validate(file: File): { valid: boolean; error?: string } {
    return validateImageFile(file);
  }

  /**
   * 生成URL
   */
  static getUrl(relativePath: string): string {
    return getImageUrl(relativePath);
  }

  /**
   * 生成缩略图URL
   */
  static getThumbnailUrl(relativePath: string): string {
    return getThumbnailUrl(relativePath);
  }
}

export default ImageService;
