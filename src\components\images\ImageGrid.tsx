/**
 * ImageGrid 图片网格组件
 * 显示图片列表，支持选择、预览和批量操作
 */

'use client';

import React from 'react';
import { Trash2, Download, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ImageCard } from './ImageCard';
import type { ImageGridProps } from '@/types/image';

export function ImageGrid({
  images,
  loading = false,
  selectedImages = new Set(),
  onSelectionChange,
  onPreview,
  onDelete,
  showActions = true,
  className,
}: ImageGridProps) {
  // 处理单个图片选择
  const handleImageSelect = (imageId: string, selected: boolean) => {
    const newSelection = new Set(selectedImages);
    if (selected) {
      newSelection.add(imageId);
    } else {
      newSelection.delete(imageId);
    }
    onSelectionChange?.(newSelection);
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedImages.size === images.length) {
      // 取消全选
      onSelectionChange?.(new Set());
    } else {
      // 全选
      const allIds = new Set(images.map(img => img.id));
      onSelectionChange?.(allIds);
    }
  };

  // 批量删除
  const handleBatchDelete = () => {
    const selectedImageObjects = images.filter(img => selectedImages.has(img.id));
    onDelete?.(selectedImageObjects);
  };

  // 批量下载
  const handleBatchDownload = () => {
    const selectedImageObjects = images.filter(img => selectedImages.has(img.id));
    selectedImageObjects.forEach(image => {
      // 验证图片数据
      if (!image || !image.relativePath) {
        console.warn('Invalid image data for download:', image);
        return;
      }

      const url = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787'}${image.relativePath}`;
      const link = document.createElement('a');
      link.href = url;
      link.download = image.relativePath.split('/').pop() || 'image';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  };

  const hasSelection = selectedImages.size > 0;
  const isAllSelected = selectedImages.size === images.length && images.length > 0;

  if (loading) {
    return (
      <div className={cn('space-y-4', className)}>
        {/* 加载状态的骨架屏 */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {Array.from({ length: 10 }).map((_, index) => (
            <div
              key={index}
              className="aspect-square bg-gray-200 rounded-lg animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className={cn('text-center py-12', className)}>
        <div className="text-gray-400 text-6xl mb-4">📷</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无图片</h3>
        <p className="text-gray-500 mb-4">
          {loading ? '正在加载图片...' : '该资源还没有上传任何图片。'}
        </p>
        {!loading && (
          <div className="text-xs text-gray-400 space-y-1">
            <p>可能的原因：</p>
            <p>• 该资源确实没有图片</p>
            <p>• 后端图片模块尚未实现</p>
            <p>• API接口返回了空数据</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* 批量操作工具栏 */}
      {showActions && onSelectionChange && (
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={isAllSelected}
                onChange={handleSelectAll}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <span className="text-sm font-medium">
                {hasSelection
                  ? `已选择 ${selectedImages.size} / ${images.length} 张`
                  : `全选 (${images.length} 张)`}
              </span>
            </label>
          </div>

          {hasSelection && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleBatchDownload}
              >
                <Download className="h-4 w-4 mr-2" />
                下载 ({selectedImages.size})
              </Button>
              {onDelete && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBatchDelete}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  删除 ({selectedImages.size})
                </Button>
              )}
            </div>
          )}
        </div>
      )}

      {/* 图片网格 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {images
          .filter(image => image && image.id && image.relativePath) // 过滤无效数据
          .map((image, index) => (
            <ImageCard
              key={image.id}
              image={image}
              selected={selectedImages.has(image.id)}
              onSelect={onSelectionChange ? (img, selected) => handleImageSelect(img.id, selected) : undefined}
              onPreview={onPreview ? (img) => onPreview(img, index) : undefined}
              onDelete={onDelete ? (img) => onDelete([img]) : undefined}
              showActions={showActions}
            />
          ))}
      </div>

      {/* 图片统计 */}
      <div className="text-center text-sm text-muted-foreground">
        显示 {images.length} 张图片
        {hasSelection && ` • 已选择 ${selectedImages.size} 张`}
      </div>
    </div>
  );
}
