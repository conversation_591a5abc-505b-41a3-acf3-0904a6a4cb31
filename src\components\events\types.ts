/**
 * EventHeader 组件相关的类型定义
 */

import { Event } from '@/schemas/event'

/**
 * 场馆信息接口
 */
export interface Venue {
  id: string
  name: string
  address?: string
  lat: number
  lng: number
}

/**
 * EventHeader 组件的 Props 接口
 */
export interface EventHeaderProps {
  /** 事件数据，可能为 null（加载中或错误状态） */
  event: Event | null
  /** 是否显示加载状态 */
  isLoading?: boolean
  /** 错误信息 */
  error?: string | null
  /** 自定义类名 */
  className?: string
}

/**
 * EventPoster 组件的 Props 接口
 */
export interface EventPosterProps {
  /** 图片 URL */
  imageUrl?: string | null
  /** 事件名称（用于 alt 文本） */
  eventName?: string | null
  /** 自定义类名 */
  className?: string
}

/**
 * EventInfo 组件的 Props 接口
 */
export interface EventInfoProps {
  /** 事件数据 */
  event: Event | null
  /** 自定义类名 */
  className?: string
}

/**
 * EventMap 组件的 Props 接口
 */
export interface EventMapProps {
  /** 场馆信息 */
  venue: Venue
  /** 是否显示加载状态 */
  isLoading?: boolean
  /** 错误信息 */
  error?: string | null
  /** 自定义类名 */
  className?: string
  /** 是否为预览模式（简化交互） */
  isPreview?: boolean
}

/**
 * 默认场馆信息（东京 Big Sight）
 */
export const DEFAULT_VENUE: Venue = {
  id: "tokyo-big-sight",
  name: "東京ビッグサイト",
  address: "東京都江東区有明3-11-1",
  lat: 35.6298,
  lng: 139.7976,
}

/**
 * 从 Event 数据创建 Venue 对象的工具函数
 * 适配新的venue关联结构
 */
export function createVenueFromEvent(event: Event | null): Venue {
  if (!event) {
    return DEFAULT_VENUE
  }

  // 新结构：如果event包含venue信息，直接使用
  if (event.venue) {
    return {
      id: event.venue.id || `venue-${event.id}`,
      name: event.venue.name || DEFAULT_VENUE.name,
      address: event.venue.address || DEFAULT_VENUE.address,
      lat: event.venue.lat || DEFAULT_VENUE.lat,
      lng: event.venue.lng || DEFAULT_VENUE.lng,
    }
  }

  // 兼容旧结构：如果有完整的场馆信息，使用事件数据
  if (event.venue_lat && event.venue_lng) {
    return {
      id: event.id,
      name: event.venue_name || DEFAULT_VENUE.name,
      address: event.venue_address,
      lat: event.venue_lat,
      lng: event.venue_lng,
    }
  }

  // 否则使用默认场馆
  return DEFAULT_VENUE
}
