'use client'

import { useState } from 'react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'

import { cn } from '@/lib/utils'
import { getValidImageUrl } from '@/lib/image-utils'
import type { EventPosterProps } from './types'

/**
 * 事件海报组件
 * 
 * 功能：
 * - 显示事件海报图片
 * - 处理图片加载失败的情况
 * - 提供加载状态和错误状态
 * - 响应式设计
 */
export default function EventPoster({
  imageUrl,
  eventName,
  className
}: EventPosterProps) {
  const t = useTranslations('EventHeader')
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
  }

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  return (
    <div className={cn(
      "flex justify-center",
      className
    )}>
      <div className="relative w-full max-w-[16rem] md:max-w-none">
        {/* 加载状态骨架屏 */}
        {imageLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-md" />
        )}
        
        {/* 图片 */}
        <Image
          src={imageError ? "/next.svg" : getValidImageUrl(imageUrl)}
          alt={eventName ? `${eventName} - ${t('imageAlt')}` : t('imageAlt')}
          width={400}
          height={560}
          className={cn(
            "rounded-md object-cover shadow-md w-full transition-opacity duration-300",
            imageLoading ? "opacity-0" : "opacity-100"
          )}
          onError={handleImageError}
          onLoad={handleImageLoad}
          priority
        />
        
        {/* 错误状态覆盖层 */}
        {imageError && imageUrl && (
          <div className="absolute inset-0 bg-gray-100 rounded-md flex items-center justify-center">
            <div className="text-center text-gray-500 p-4">
              <div className="text-2xl mb-2">🖼️</div>
              <p className="text-sm">图片加载失败</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
