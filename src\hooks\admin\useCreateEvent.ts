import { useQueryClient } from "@tanstack/react-query";

import {
  usePostAdminEvents,
  type PostAdminEventsRequestBody
} from "@/api/generated/ayafeedComponents";
import { queryKeys } from "@/constants/queryKeys";
import type { MultilingualEventInput } from "@/schemas/event";

/**
 * 将表单数据转换为 API 请求格式
 */
function transformEventInputToApiFormat(input: MultilingualEventInput): PostAdminEventsRequestBody {
  return {
    name_en: input.name_en,
    name_ja: input.name_ja,
    name_zh: input.name_zh,
    date_en: input.date_en,
    date_ja: input.date_ja,
    date_zh: input.date_zh,
    date_sort: input.date_sort,
    image_url: input.image_url || null,
    venue_id: input.venue_id,
    url: input.url || null,
  };
}

export function useCreateEvent() {
  const qc = useQueryClient();

  // 使用生成的 API hook
  const createEventMutation = usePostAdminEvents({
    onSuccess: () => {
      qc.invalidateQueries({ queryKey: queryKeys.adminEvents() });
      // 刷新前台 /events 列表缓存
      qc.invalidateQueries({ predicate: ({ queryKey }) => Array.isArray(queryKey) && queryKey[0] === "events" });
    },
  });

  return {
    ...createEventMutation,
    mutate: (input: MultilingualEventInput, options?: any) => {
      const apiPayload = transformEventInputToApiFormat(input);
      createEventMutation.mutate(
        { body: apiPayload },
        {
          ...options,
          onSuccess: (data, variables, context) => {
            // 显示成功提示
            if (typeof window !== 'undefined') {
              console.log('创建成功');
            }
            options?.onSuccess?.(data, variables, context);
          },
          onError: (error, variables, context) => {
            // 显示错误提示
            if (typeof window !== 'undefined') {
              console.error('创建失败:', error);
            }
            options?.onError?.(error, variables, context);
          }
        }
      );
    }
  };
}