'use client'

import Image from "next/image";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";

import { useGetEvents } from "@/api/generated/ayafeedComponents";
import * as Schemas from "@/api/generated/ayafeedSchemas";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import EventCardSkeleton from "@/components/events/EventCardSkeleton";
import EmptyState from "@/components/events/EmptyState";
import { useDebounce } from "@/hooks";
import { getValidImageUrl } from "@/lib/image-utils";

const PAGE_SIZE = 12;

export default function EventsList() {
  // 搜索关键字
  const [keyword, setKeyword] = useState("");
  const debouncedKeyword = useDebounce(keyword, 300);

  // 分页
  const [page, setPage] = useState(1);

  // 当关键字变化时重置页数
  useEffect(() => {
    setPage(1);
  }, [debouncedKeyword]);

  // 组装查询变量
  const vars = useMemo(
    () =>
      ({
        queryParams: {
          page: String(page),
          pageSize: String(PAGE_SIZE),
          keyword: debouncedKeyword || undefined,
        },
      }) as const,
    [page, debouncedKeyword]
  );

  const { data, isLoading, isFetching } = useGetEvents(vars, {
    staleTime: 60 * 1000,
  });

  // events 与分页信息
  const events = (data?.items ?? []) as Schemas.PaginatedResult["items"];
  const total = data?.total ?? 0;
  const hasMore = page * PAGE_SIZE < total;

  return (
    <div className="space-y-6" data-testid="events-list">
      {/* 搜索框 */}
      <Input
        placeholder="搜索展会…"
        value={keyword}
        onChange={(e) => setKeyword(e.target.value)}
        className="max-w-sm"
      />

      {/* 列表区域 */}
      <div data-testid="events-grid">
        {isLoading && page === 1 ? (
          <SkeletonGrid count={PAGE_SIZE} />
        ) : events.length === 0 ? (
          <EmptyState message="暂无展会信息" />
        ) : (
          <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
            {events.map((evt) => (
              <EventCard key={evt.id} evt={evt as any} />
            ))}
            {/* 下一页加载中骨架 */}
            {isFetching && page > 1 && <SkeletonGrid count={4} />}
          </div>
        )}
      </div>

      {/* 加载更多 */}
      {hasMore && (
        <div className="flex justify-center">
          <Button
            onClick={() => setPage((p) => p + 1)}
            disabled={isFetching}
            variant="outline"
          >
            {isFetching ? "加载中…" : "加载更多"}
          </Button>
        </div>
      )}
    </div>
  );
}

/** 事件卡片 */
function EventCard({ evt }: { evt: any }) {
  return (
    <Link
      href={`/events/${evt.id}`}
      className="group focus:outline-none"
      data-testid="event-card"
    >
      <Card className="overflow-hidden group-hover:shadow-lg transition-shadow">
        <CardHeader className="p-0">
          <Image
            src={getValidImageUrl(evt.image_url)}
            alt={evt.name}
            width={400}
            height={240}
            className="w-full h-48 object-cover"
          />
        </CardHeader>
        <CardContent className="p-4 space-y-1">
          <h3 className="font-semibold truncate">{evt.name}</h3>
          <p className="text-sm text-muted-foreground">
            {evt.date}
            {evt.venue_name ? ` ・ ${evt.venue_name}` : ""}
          </p>
        </CardContent>
      </Card>
    </Link>
  );
}

/** 骨架网格 */
function SkeletonGrid({ count }: { count: number }) {
  return (
    <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
      {Array.from({ length: count }).map((_, i) => (
        <EventCardSkeleton key={i} />
      ))}
    </div>
  );
} 