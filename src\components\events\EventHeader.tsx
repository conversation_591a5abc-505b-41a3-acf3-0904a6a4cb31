'use client'

import { useTranslations } from 'next-intl'

import { cn } from '@/lib/utils'
import EventPoster from './EventPoster'
import EventInfo from './EventInfo'
import EventMap from './EventMap'
import { createVenueFromEvent, type EventHeaderProps } from './types'

/**
 * 事件头部组件
 *
 * 功能：
 * - 显示事件海报、基本信息和场馆地图
 * - 支持加载状态和错误处理
 * - 响应式布局设计
 * - 完整的国际化支持
 * - 类型安全的实现
 */
export default function EventHeader({
  event,
  isLoading = false,
  error = null,
  className
}: EventHeaderProps) {
  const t = useTranslations('EventHeader')
  const venue = createVenueFromEvent(event)

  return (
    <header className={cn(
      "bg-background text-foreground",
      className
    )}>
      <div className="max-w-7xl mx-auto px-4 py-10">
        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-[auto_1fr] gap-10">
          {/* 左侧：事件海报 */}
          <EventPoster
            imageUrl={event?.image_url}
            eventName={event?.name}
            className="lg:w-64"
          />

          {/* 右侧：事件信息 */}
          <EventInfo
            event={event}
            className="flex-1"
          />
        </div>

        {/* 地图部分 */}
        <div className="mt-10">
          <h2 className="text-xl font-semibold mb-4">
            {t('venue')}位置
          </h2>
          <EventMap
            venue={venue}
            isLoading={isLoading}
            error={error}
            className="h-[18rem]"
          />
        </div>
      </div>
    </header>
  )
}
